# 激光循迹系统使用说明

## 系统概述

本系统实现了一个基于STM32F103的激光循迹系统，通过K230发送角点坐标和激光点坐标，控制舵机带动激光笔进行矩形路径循迹。

## 硬件连接

### 1. 串口连接
- **K230 TX** → **STM32 PB11** (UART3_RX)
- **K230 RX** → **STM32 PB10** (UART3_TX)
- **波特率**: 115200

### 2. 舵机连接
- **X轴舵机**: 连接到PA0 (TIM2_CH1)
- **Y轴舵机**: 连接到PA6 (TIM3_CH1)
- **激光笔**: 固定在舵机上

## 数据协议

### 1. 角点坐标包（17字节）
```
帧格式：[0xAA, x0_H, x0_L, y0_H, y0_L, x1_H, x1_L, y1_H, y1_L, 
         x2_H, x2_L, y2_H, y2_L, x3_H, x3_L, y3_H, y3_L, 0x55]
```
- **0xAA**: 帧头
- **x0_H, x0_L**: 第1个角点X坐标（高字节在前）
- **y0_H, y0_L**: 第1个角点Y坐标（高字节在前）
- **...**: 第2、3、4个角点坐标
- **0x55**: 帧尾

### 2. 激光点坐标包（6字节）
```
帧格式：[0xAA, x_H, x_L, y_H, y_L, 0x55]
```
- **0xAA**: 帧头
- **x_H, x_L**: 激光点X坐标（高字节在前）
- **y_H, y_L**: 激光点Y坐标（高字节在前）
- **0x55**: 帧尾

## 控制指令

### 串口指令
- **T1**: 手动开始激光循迹（备用功能）
- **T0**: 手动停止激光循迹（备用功能）
- **S2**: 重置舵机到中心位置

### 自动控制
- 系统接收到角点坐标后自动等待激光点
- 检测到第一个激光点坐标时自动开始循迹
- 走完一圈后自动停止循迹

## 系统工作流程

### 1. 初始化阶段
1. 系统启动，初始化PID控制器
2. 舵机回到中心位置
3. 等待角点坐标数据

### 2. 路径生成阶段
1. 接收K230发送的4个角点坐标
2. 计算矩形边界
3. 生成矩形路径点（每5像素一个点）
4. 路径生成完成，等待激光点数据

### 3. 自动循迹阶段
1. 系统检测到第一个激光点坐标时自动开始循迹
2. 计算激光点与目标路径点的误差
3. 使用PID算法计算舵机控制量
4. 控制舵机移动，使激光点跟踪路径
5. 走完一圈后自动停止循迹

### 4. 完成阶段
1. 系统输出"Path completed! Tracking stopped."
2. 等待新的角点坐标重新开始

## PID控制参数

### X轴舵机PID参数
- **Kp**: 0.5（比例系数）
- **Ki**: 0.01（积分系数）
- **Kd**: 0.1（微分系数）

### Y轴舵机PID参数
- **Kp**: 0.5（比例系数）
- **Ki**: 0.01（积分系数）
- **Kd**: 0.1（微分系数）

## 坐标映射

### 图像坐标范围
- **X坐标**: 0-640
- **Y坐标**: 0-480

### 舵机角度范围
- **X轴舵机**: 0-270度
- **Y轴舵机**: 0-180度

### 坐标转换公式
```
X轴舵机角度 = (X坐标 / 640) * 270
Y轴舵机角度 = (Y坐标 / 480) * 180
```

## 使用方法

### 1. 系统启动
1. 编译并下载程序到STM32F103
2. 连接K230和舵机
3. 系统自动初始化

### 2. 自动循迹流程
1. K230发送角点坐标包
2. 系统生成矩形路径，等待激光点
3. K230发送第一个激光点坐标
4. 系统自动开始循迹
5. K230持续发送激光点坐标
6. 系统自动控制舵机循迹
7. 走完一圈后自动停止

### 3. 重新开始
1. 发送新的角点坐标包
2. 系统重新生成路径
3. 重复上述流程

### 4. 手动控制（备用）
1. 发送"T1"指令手动开始循迹
2. 发送"T0"指令手动停止循迹
3. 发送"S2"指令重置舵机位置

## 调试信息

系统会输出以下调试信息：
```
Laser tracking system initialized
Corners received: (100,100) (500,100) (500,300) (100,300)
Rectangle path generated with 120 points
Waiting for laser point to start tracking...
Laser detected, starting automatic tracking...
Laser position: (200, 150)
Tracking: target(100,100) laser(200,150) error(-100.0,-50.0) angle(45.0,56.3)
Reached point 1/120
...
Reached point 120/120
Path completed! Tracking stopped.
```

## 参数调整

### 1. PID参数调整
如果循迹效果不好，可以调整PID参数：
```c
#define PID_X_KP 0.5f  // 增大提高响应速度，但可能超调
#define PID_X_KI 0.01f // 增大消除稳态误差
#define PID_X_KD 0.1f  // 增大抑制超调
```

### 2. 路径点密度调整
```c
// 在generate_rectangle_path函数中调整步长
for (uint16_t x = min_x; x <= max_x && index < MAX_PATH_POINTS; x += 5) {
    // 5是步长，减小步长增加路径点密度
}
```

### 3. 到达容差调整
```c
if (distance < 10.0f) {  // 10像素的容差
    // 增大容差使循迹更平滑，减小容差提高精度
}
```

## 故障排除

### 1. 舵机不响应
- 检查PWM信号输出
- 检查舵机电源供电
- 检查舵机连接

### 2. 循迹精度差
- 调整PID参数
- 检查坐标映射是否正确
- 调整路径点密度

### 3. 数据接收错误
- 检查串口连接
- 检查数据包格式
- 检查波特率设置

## 注意事项

1. **坐标范围**: 确保K230发送的坐标在0-640×0-480范围内
2. **数据格式**: 严格按照协议格式发送数据
3. **舵机限位**: 注意舵机角度范围，避免超出机械限位
4. **PID调参**: 根据实际系统特性调整PID参数
5. **电源稳定**: 确保舵机有足够的电源供应

## 扩展功能

### 1. 多路径支持
可以扩展支持多种路径形状（圆形、三角形等）

### 2. 自适应PID
可以根据误差大小动态调整PID参数

### 3. 路径优化
可以实现更平滑的路径生成算法

### 4. 状态反馈
可以添加更多状态反馈信息 