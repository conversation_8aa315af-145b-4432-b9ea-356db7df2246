#ifndef __BSP_SYSTEM_H
#define __BSP_SYSTEM_H


#include "tim.h"
#include "stdio.h"
#include "stdbool.h"
#include "string.h"
#include "usart.h"

#include "servo.h"
#include "uart_app.h"
#include "pid_controller.h"
#include "data_precess.h"
#include "laser_tracking.h"


extern uint8_t uart1_rx_dma_buf[128];
extern uint8_t uart1_rx_buf[128];


void scheduler_run(void);


// ·������
void follow_path(void);
void parse_points(char* buf);
void generate_path_points(void);


#endif

