# 激光循迹系统测试指南

## 1. 测试准备

### 1.1 硬件连接
1. **舵机连接**
   - X轴舵机连接到PA0 (TIM2_CH1)
   - Y轴舵机连接到PA6 (TIM3_CH1)
   - 舵机电源确保稳定5V供电

2. **串口连接**
   - UART1：用于调试输出和测试指令输入
   - UART3：用于接收角点坐标和激光点坐标

### 1.2 软件准备
1. **编译并下载程序**
   - 使用Keil MDK编译项目
   - 下载到STM32F103开发板

2. **串口调试工具**
   - 波特率：115200
   - 数据位：8
   - 停止位：1
   - 校验位：无
   - 流控制：无

## 2. 测试流程

### 2.1 基本功能测试

#### 步骤1：发送测试指令
在串口调试工具中发送：
```
TEST
```

**预期输出**：
```
Starting tracking system test...
=== Testing Tracking System ===
Test corners set:
Corner 1: (100, 100)
Corner 2: (500, 100)
Corner 3: (500, 300)
Corner 4: (100, 300)
Rectangle bounds: X(100-500), Y(100-300)
Path point 0: (100, 300) - Left edge
...
Rectangle path generated with XX points
Test laser position: (150, 150)
Test tracking started
=== Tracking Control Debug ===
...
=== Test Completed ===
```

#### 步骤2：观察舵机行为
- 舵机应该移动到对应目标点的位置
- X轴舵机应该移动到约42度（100/640*270）
- Y轴舵机应该移动到约56度（100/480*180）

### 2.2 角点坐标测试

#### 步骤1：发送角点坐标
在串口调试工具中发送：
```
AA 00 64 00 64 01 F4 00 64 01 F4 01 2C 00 64 01 2C 55
```

**数据解析**：
- 帧头：AA
- 角点1：x=0x0064(100), y=0x0064(100) - 左上
- 角点2：x=0x01F4(500), y=0x0064(100) - 右上
- 角点3：x=0x01F4(500), y=0x012C(300) - 右下
- 角点4：x=0x0064(100), y=0x012C(300) - 左下
- 帧尾：55

**预期输出**：
```
Detected hex string format packet
Processing hex string packet
Converted to binary (18 bytes): AA 00 64 00 64 01 F4 00 64 01 F4 01 2C 00 64 01 2C 55
Processing binary data: size=18
Processing corners packet from binary data
=== Processing Corner Coordinates ===
Corner 1: (0x0064, 0x0064) = (100, 100)
Corner 2: (0x01F4, 0x0064) = (500, 100)
Corner 3: (0x01F4, 0x012C) = (500, 300)
Corner 4: (0x0064, 0x012C) = (100, 300)
Rectangle bounds: X(100-500), Y(100-300)
Path point 0: (100, 300) - Left edge
...
Rectangle path generated with XX points
Path generation completed, ready for tracking
```

### 2.3 激光点坐标测试

#### 步骤1：发送第一个激光点坐标
在串口调试工具中发送：
```
AA 00 96 00 96 55
```

**数据解析**：
- 帧头：AA
- X坐标：0x0096(150)
- Y坐标：0x0096(150)
- 帧尾：55

**预期输出**：
```
Detected hex string format packet
Processing hex string packet
Converted to binary (6 bytes): AA 00 96 00 96 55
Processing binary data: size=6
Processing laser packet from binary data
=== Laser Point Coordinates Received ===
Laser point position: (0x0096, 0x0096) = (150, 150)
=== Auto Start Laser Tracking ===
Laser point detected, starting auto tracking...
Path ready: Yes
Tracking active: Yes
Path completed: No
Executing tracking control...
=== Tracking Control Debug ===
...
```

#### 步骤2：持续发送不同位置的激光点
发送一系列不同位置的激光点坐标，观察舵机是否按照矩形路径移动：

```
AA 00 C8 00 C8 55  // (200, 200)
AA 00 FA 00 FA 55  // (250, 250)
AA 01 2C 01 2C 55  // (300, 300)
...
```

### 2.4 完整循迹测试

#### 步骤1：重置系统
发送重置指令：
```
S2
```

#### 步骤2：发送角点坐标
```
AA 00 64 00 64 01 F4 00 64 01 F4 01 2C 00 64 01 2C 55
```

#### 步骤3：按照路径点顺序发送激光点坐标
1. 左边（从下到上）：
   ```
   AA 00 64 01 2C 55  // (100, 300) - 左下
   AA 00 64 00 FA 55  // (100, 250)
   AA 00 64 00 C8 55  // (100, 200)
   AA 00 64 00 96 55  // (100, 150)
   AA 00 64 00 64 55  // (100, 100) - 左上
   ```

2. 上边（从左到右）：
   ```
   AA 00 96 00 64 55  // (150, 100)
   AA 00 C8 00 64 55  // (200, 100)
   ...
   AA 01 F4 00 64 55  // (500, 100) - 右上
   ```

3. 右边（从上到下）：
   ```
   AA 01 F4 00 96 55  // (500, 150)
   AA 01 F4 00 C8 55  // (500, 200)
   ...
   AA 01 F4 01 2C 55  // (500, 300) - 右下
   ```

4. 下边（从右到左）：
   ```
   AA 01 C2 01 2C 55  // (450, 300)
   AA 01 90 01 2C 55  // (400, 300)
   ...
   AA 00 64 01 2C 55  // (100, 300) - 左下（回到起点）
   ```

## 3. 故障排除

### 3.1 舵机不移动
- 检查PWM信号是否正确（50Hz，500-2500us）
- 检查舵机电源是否稳定
- 检查串口输出中的角度计算是否正确

### 3.2 循迹不正确
- 检查路径点生成是否正确
- 检查坐标转换是否正确
- 检查PID参数是否合适

### 3.3 无法识别数据
- 确认数据格式正确
- 检查帧头(0xAA)和帧尾(0x55)
- 检查串口波特率设置

## 4. 调试技巧

### 4.1 使用TEST指令
发送`TEST`指令可以快速验证基本功能，无需外部输入。

### 4.2 观察调试输出
系统会输出详细的调试信息，包括：
- 角点坐标和路径点生成
- 激光点位置和目标点位置
- 舵机角度计算和控制
- 到达判断和路径完成状态

### 4.3 单步测试
可以单独测试路径生成和循迹控制：
1. 先发送角点坐标，检查路径生成
2. 再发送激光点坐标，检查循迹控制

---

**文档版本**：v1.0  
**创建时间**：2025-01-28  
**负责人**：Alex (工程师)
