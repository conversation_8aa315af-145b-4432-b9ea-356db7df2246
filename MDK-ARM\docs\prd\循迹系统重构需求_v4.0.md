# 激光循迹系统重构需求文档 v4.0

## 1. 文档信息

| 项目名称 | 激光循迹系统重构 |
|---------|-----------------|
| 版本号 | v4.0 |
| 创建日期 | 2025-01-29 |
| 负责人 | Emma (产品经理) |
| 状态 | 需求确认中 |

## 2. 背景与问题陈述

### 2.1 当前问题
- **问题1**：激光点往框外运动，不按矩形边循迹
- **问题2**：激光点没有按照矩形框规划路径的坐标点进行追踪
- **问题3**：缺少寻找黑框路径最近点的机制

### 2.2 根本原因
1. **循迹策略错误**：强制按路径点索引顺序移动，忽略物理距离
2. **缺少最近点算法**：没有寻找离激光点最近的边界点
3. **路径跟踪不合理**：不符合沿边循迹的物理规律

## 3. 目标与成功指标

### 3.1 项目目标
1. **主要目标**：实现激光点沿黑框边界精确循迹
2. **次要目标**：提高循迹稳定性和完成率
3. **用户体验目标**：循迹过程平滑自然，符合预期

### 3.2 关键结果(KRs)
- **KR1**：激光点能找到并到达最近的矩形边界点
- **KR2**：激光点能沿着矩形边界连续移动
- **KR3**：完整循迹一圈的成功率达到95%以上
- **KR4**：循迹过程中不出现往框外运动的情况

### 3.3 反向指标
- 循迹启动时间不超过2秒
- 舵机移动平滑度不降低
- 系统资源占用不增加超过20%

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**：电子竞赛参赛者
- **使用场景**：激光循迹演示和测试
- **技术水平**：具备基础的嵌入式开发能力

### 4.2 用户故事
**作为**电子竞赛参赛者  
**我希望**激光点能准确沿着黑框边界循迹  
**以便于**完成循迹演示任务并获得高分

**验收标准**：
- 激光点启动后能自动找到最近的黑框边界
- 激光点沿着黑框边界连续移动，不偏离轨道
- 完成一圈循迹后自动停止

## 5. 功能规格详述

### 5.1 核心功能：最近点寻找算法

**功能描述**：实现寻找黑框路径最近点的算法

**输入**：
- 当前激光点坐标 (x, y)
- 矩形路径点数组 path_points[]
- 路径点数量 path_count

**输出**：
- 最近路径点的索引 nearest_index
- 到最近点的距离 min_distance

**算法逻辑**：
```c
int find_nearest_path_point(point_t laser_pos, point_t* path_points, int path_count) {
    int nearest_index = 0;
    float min_distance = calculate_distance(laser_pos, path_points[0]);
    
    for (int i = 1; i < path_count; i++) {
        float distance = calculate_distance(laser_pos, path_points[i]);
        if (distance < min_distance) {
            min_distance = distance;
            nearest_index = i;
        }
    }
    return nearest_index;
}
```

### 5.2 核心功能：沿边循迹控制

**功能描述**：实现沿着矩形边界的连续循迹控制

**工作模式**：
1. **寻找阶段**：激光点移动到最近的边界点
2. **循迹阶段**：沿着边界按顺序移动到下一个点

**状态机设计**：
```
INIT → FINDING_NEAREST → TRACKING_EDGE → COMPLETED
  ↑                                ↓
  └──────── RESET ←────────────────┘
```

### 5.3 核心功能：智能目标点选择

**功能描述**：根据当前状态智能选择下一个目标点

**选择策略**：
- **初始状态**：选择最近的路径点
- **循迹状态**：选择当前点的下一个相邻点
- **边界切换**：检测到边界切换时，选择新边界的起始点

## 6. 范围定义

### 6.1 包含功能(In Scope)
- ✅ 最近点寻找算法实现
- ✅ 沿边循迹控制逻辑
- ✅ 智能目标点选择机制
- ✅ 循迹状态管理
- ✅ 边界检测和切换逻辑

### 6.2 排除功能(Out of Scope)
- ❌ 多边形路径支持（仅支持矩形）
- ❌ 动态路径规划
- ❌ 障碍物避让功能
- ❌ 路径优化算法

## 7. 依赖与风险

### 7.1 内部依赖
- **依赖1**：现有的PID控制系统
- **依赖2**：舵机控制接口
- **依赖3**：坐标转换函数

### 7.2 外部依赖
- **依赖1**：K230提供准确的角点坐标
- **依赖2**：K230提供实时的激光点坐标
- **依赖3**：舵机硬件响应正常

### 7.3 潜在风险
- **风险1**：算法复杂度增加可能影响实时性
- **风险2**：边界点判断可能存在误差
- **风险3**：状态切换可能导致循迹中断

### 7.4 风险缓解策略
- **缓解1**：优化算法实现，使用高效的距离计算
- **缓解2**：增加边界点判断的容差机制
- **缓解3**：添加状态切换的平滑过渡逻辑

## 8. 发布初步计划

### 8.1 开发阶段
- **阶段1**：最近点寻找算法实现（1天）
- **阶段2**：循迹控制逻辑重构（1天）
- **阶段3**：状态管理和边界检测（1天）
- **阶段4**：集成测试和优化（1天）

### 8.2 测试计划
- **单元测试**：各个算法模块独立测试
- **集成测试**：完整循迹流程测试
- **性能测试**：实时性和稳定性测试
- **用户验收测试**：实际场景下的循迹效果验证

### 8.3 发布标准
- 所有功能测试通过
- 循迹成功率达到95%以上
- 无往框外运动的异常情况
- 用户验收测试通过

## 9. 技术实现要点

### 9.1 关键算法
1. **欧几里得距离计算**：`sqrt((x2-x1)² + (y2-y1)²)`
2. **最近点搜索**：线性搜索，时间复杂度O(n)
3. **边界检测**：基于路径点索引和位置判断

### 9.2 数据结构设计
```c
typedef enum {
    TRACKING_INIT,
    TRACKING_FINDING_NEAREST,
    TRACKING_EDGE,
    TRACKING_COMPLETED
} tracking_state_t;

typedef struct {
    tracking_state_t state;
    int current_target_index;
    int nearest_point_index;
    bool is_on_edge;
} tracking_context_t;
```

### 9.3 接口设计
- `int find_nearest_path_point(point_t laser_pos, point_t* path_points, int path_count)`
- `void update_tracking_state(tracking_context_t* context)`
- `int get_next_target_point(tracking_context_t* context)`
