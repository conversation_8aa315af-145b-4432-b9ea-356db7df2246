# K210黑框矩形循迹使用说明

## 1. 功能概述

本系统实现了K210与STM32的协同工作，用于红色激光笔按照黑框矩形轨迹进行顺序循迹。系统支持：

- ✅ K210实时检测黑框矩形并离散化为轨迹点
- ✅ K210上电时发送全部目标点坐标给STM32
- ✅ K210以30Hz频率实时发送红色激光点当前物理坐标
- ✅ STM32根据轨迹点进行顺序循迹控制
- ✅ 保证循迹顺序：左下→左上→右上→右下→左下

## 2. 系统架构

```
┌─────────────┐    UART3     ┌─────────────┐    PWM      ┌─────────────┐
│    K210     │ ←----------→ │   STM32     │ ----------→ │   舵机云台   │
│  视觉处理   │   115200     │  控制器     │             │   激光笔    │
└─────────────┘              └─────────────┘             └─────────────┘
       ↑                            ↑                           ↓
   黑框检测                      循迹算法                    激光定位
   坐标计算                      PID控制                     轨迹跟踪
```

## 3. 使用步骤

### 3.1 硬件准备
1. **连接UART3**: K210的UART接口连接到STM32的UART3 (PA6-TX, PA7-RX)
2. **舵机连接**: X轴舵机连接PA0 (TIM2_CH1)，Y轴舵机连接PA6 (TIM3_CH1)
3. **激光笔**: 确保红色激光笔正常工作
4. **电源**: 确保K210和STM32都有稳定的电源供应

### 3.2 软件配置
1. **K210程序**: 烧录支持黑框检测和UART通信的程序
2. **STM32程序**: 烧录本舵机控制程序
3. **串口调试**: 连接UART1用于调试信息输出

### 3.3 操作流程

#### 步骤1: 系统初始化
```
1. 给K210和STM32上电
2. 等待系统初始化完成 (约3秒)
3. 通过串口调试工具连接STM32的UART1
```

#### 步骤2: 启动循迹任务
```
1. 在串口调试工具中发送: S4
2. 系统响应: "Starting K210 Track Sequence Task !!!"
3. STM32向K210发送: "REQ_INIT\r\n"
```

#### 步骤3: K210发送轨迹数据
```
1. K210检测黑框矩形
2. 计算四个顶点坐标 (左下、左上、右上、右下)
3. 发送轨迹初始化数据:
   "INIT:P1x100y400,P2x100y100,P3x500y100,P4x500y400,END\r\n"
4. STM32响应: "ACK:INIT_OK\r\n"
```

#### 步骤4: 开始循迹
```
1. K210开始以30Hz频率发送当前激光点位置:
   "POS:x123y456\r\n"
2. STM32根据位置数据控制舵机
3. 激光笔按照设定轨迹顺序移动
```

#### 步骤5: 停止循迹
```
1. 发送停止指令: T
2. 或者发送: STOP_TRACK (通过UART3)
3. 系统停止循迹，舵机回到中位
```

## 4. 调试指令

### 4.1 基本控制指令 (通过UART1发送)
| 指令 | 功能 | 响应 |
|------|------|------|
| `S4` | 启动K210循迹任务 | "Starting K210 Track Sequence Task !!!" |
| `T` | 停止所有任务 | "Stoping" |
| `FHT` | 暂停/恢复任务 | 切换暂停状态 |
| `TEST_K210` | 测试K210协议 | 显示测试结果 |

### 4.2 测试指令详解

#### TEST_K210指令
发送`TEST_K210`可以测试通信协议的正确性：
```
🧪 Starting K210 protocol test...
✅ Track init test passed: 4 points received
   P1: (100, 400)
   P2: (100, 100)  
   P3: (500, 100)
   P4: (500, 400)
✅ Position parse test passed: (123, 456)
📐 Angle conversion: X=52.3°, Y=168.7°
📊 Current track state: IDLE
🎯 Current target index: 0/4
```

## 5. 状态监控

### 5.1 系统状态
- **IDLE**: 空闲状态，等待轨迹数据
- **INIT_RECEIVED**: 已接收轨迹点，准备开始循迹
- **RUNNING**: 正在执行循迹
- **COMPLETED**: 一轮循迹完成，准备开始下一轮

### 5.2 调试信息
系统会通过UART1输出详细的调试信息：
```
Track points received: 4
Current target: P1(100,400)
Laser position: (123,456)
Servo angles: X=52.3°, Y=168.7°
Distance to target: 15.2 pixels
Switching to next target: P2(100,100)
```

## 6. 坐标系说明

### 6.1 像素坐标系
- **原点**: 图像左上角 (0, 0)
- **X轴**: 向右为正，范围 0-640
- **Y轴**: 向下为正，范围 0-480
- **单位**: 像素

### 6.2 舵机角度坐标系
- **X轴舵机**: 0-270度，对应像素X坐标0-640
- **Y轴舵机**: 0-180度，对应像素Y坐标0-480
- **转换公式**:
  - `angle_x = pixel_x * 270.0 / 640.0`
  - `angle_y = pixel_y * 180.0 / 480.0`

### 6.3 循迹顺序
矩形四个顶点的循迹顺序：
```
P2 (左上) ────────→ P3 (右上)
   ↑                   ↓
   │                   │
   │                   │
P1 (左下) ←──────── P4 (右下)
```
循迹路径：P1→P2→P3→P4→P1 (左下→左上→右上→右下→左下)

## 7. 常见问题与解决方案

### 7.1 通信问题
**问题**: K210与STM32无法通信
**解决方案**:
1. 检查UART3连接是否正确
2. 确认波特率设置为115200
3. 检查电源和地线连接
4. 使用示波器检查信号质量

**问题**: 接收到的数据格式错误
**解决方案**:
1. 检查K210发送的数据格式是否符合协议
2. 确认字符串结尾有`\r\n`
3. 检查数据长度是否超过缓冲区大小

### 7.2 循迹问题
**问题**: 激光笔不按顺序循迹
**解决方案**:
1. 检查轨迹点坐标是否正确
2. 确认`current_track_index`变量更新正常
3. 调整到达判断的距离阈值 (当前10像素)

**问题**: 循迹精度不够
**解决方案**:
1. 调整PID参数 (Kp=0.15, Ki=0.01, Kd=0.05)
2. 减小到达判断的距离阈值
3. 提高K210位置数据发送频率

### 7.3 舵机问题
**问题**: 舵机不响应或抖动
**解决方案**:
1. 检查PWM信号是否正常 (20ms周期，500-2500us脉宽)
2. 确认舵机电源供应充足
3. 调整PID参数，降低Kp值

## 8. 性能优化建议

### 8.1 响应速度优化
- 提高K210位置数据发送频率到50Hz
- 优化STM32的任务调度，减少uart3_task的调用间隔
- 使用DMA方式处理UART数据，减少CPU占用

### 8.2 精度优化
- 增加轨迹点数量，使轨迹更平滑
- 实现轨迹插值算法，在轨迹点之间生成中间点
- 使用卡尔曼滤波对位置数据进行平滑处理

### 8.3 稳定性优化
- 添加通信超时检测和重连机制
- 实现数据校验，确保接收数据的完整性
- 添加异常处理，防止系统崩溃

## 9. 扩展功能

### 9.1 多边形轨迹支持
当前系统支持最多20个轨迹点，可以扩展支持任意多边形：
```c
#define MAX_TRACK_POINTS 50  // 增加支持的轨迹点数
```

### 9.2 轨迹速度控制
可以为每个轨迹段设置不同的移动速度：
```c
typedef struct {
    int16_t x, y;
    float speed;  // 移动速度系数
} TrackPoint_t;
```

### 9.3 轨迹记录与回放
添加轨迹记录功能，可以保存和回放历史轨迹：
```c
void save_track_to_flash(void);
void load_track_from_flash(void);
void replay_saved_track(void);
```

## 10. 版本信息

| 版本 | 日期 | 功能特性 | 状态 |
|------|------|----------|------|
| v1.0 | 2025-01-24 | 基本循迹功能，支持矩形轨迹 | ✅ 完成 |
| v1.1 | 计划中 | 多边形轨迹支持 | 🔄 开发中 |
| v1.2 | 计划中 | 轨迹速度控制 | 📋 计划中 |

---

**文档编写**: Alex (工程师)  
**最后更新**: 2025-01-24  
**技术支持**: 通过UART1发送调试信息获取技术支持