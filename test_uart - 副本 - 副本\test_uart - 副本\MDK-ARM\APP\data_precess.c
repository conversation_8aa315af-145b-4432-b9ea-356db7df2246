#include "bsp_system.h"
#include <stdlib.h>

// 使用data_precess.h中已定义的Point_t类型

#define CORNER_NUM 4
Point_t corners[CORNER_NUM]; // 存储四个角点
uint8_t corners_received = 0; // 标志位

#define PATH_POINTS_NUM 80
Point_t path_points[PATH_POINTS_NUM];
uint8_t path_points_received = 0;

Point_t laser_point;

void generate_path_points(void) {
    int idx = 0;
    for (int i = 0; i < CORNER_NUM; i++) {
        Point_t start = corners[i];
        Point_t end = corners[(i+1)%CORNER_NUM];
        for (int j = 0; j < 20; j++) {
            float t = (float)j / 20.0f;
            path_points[idx].x = (uint16_t)(start.x + (end.x - start.x) * t);
            path_points[idx].y = (uint16_t)(start.y + (end.y - start.y) * t);
            idx++;
        }
    }
    path_points_received = 1; // 路径生成完成
}

// 处理串口数据
// 数据 data为一帧数据，格式: 0xAA x(ASCII) y(ASCII) 0x55
void process_uart_data(uint8_t *data, uint16_t len) {
    if (len < 8) return;
    if (data[0] != 0xAA || data[len-1] != 0x55) return;

    char xbuf[5] = {0}, ybuf[5] = {0};
    memcpy(xbuf, &data[1], 4);
    memcpy(ybuf, &data[5], 4);
    float x = atof(xbuf);
    float y = atof(ybuf);

    static uint8_t corner_idx = 0;
    if (!corners_received) {
        corners[corner_idx].x = (uint16_t)x;
        corners[corner_idx].y = (uint16_t)y;
        corner_idx++;
        if (corner_idx >= CORNER_NUM) {
            corners_received = 1;
            corner_idx = 0;
            // 角点接收完成，自动生成路径点
            generate_path_points();
        }
    } else {
        // 角点接收完成，数据为激光点
        laser_point.x = (uint16_t)x;
        laser_point.y = (uint16_t)y;
    }
}






