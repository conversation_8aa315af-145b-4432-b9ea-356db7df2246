# 舵机控制问题诊断指南

## 问题描述
发送串口指令（如x+1, A, S2）到UART3后，舵机没有响应。

## 已修复的问题

### 1. 定时器配置问题
**问题**：TIM2的PWM频率是500Hz，舵机需要50Hz
**修复**：将TIM2的周期从2000改为20000
```c
// 修复前
htim2.Init.Period = 2000-1;  // 500Hz

// 修复后  
htim2.Init.Period = 20000-1; // 50Hz
```

### 2. 串口接收回调问题
**问题**：uart1_rx_flag设置逻辑错误
**修复**：只在UART3接收到数据时设置标志
```c
// 修复前
if(huart->Instance == USART3) {
    // 复制数据
}
uart1_rx_flag = true;  // 错误：总是设置

// 修复后
if(huart->Instance == USART3) {
    // 复制数据
    uart1_rx_flag = true;  // 正确：只在UART3时设置
}
```

### 3. 添加调试信息
**新增**：详细的调试输出，帮助诊断问题
```c
printf("Received %d bytes: %s\r\n", Size, uart1_rx_buf);
printf("Processing command: %s\r\n", uart1_rx_buf);
printf("Set servo angle: %.1f (max: %.1f), PWM: %lu us\r\n", angle, max_angle, pulse_width);
```

## 测试步骤

### 1. 编译和下载
1. 使用Keil MDK编译项目
2. 下载到STM32F103开发板
3. 观察串口输出

### 2. 预期启动输出
```
System initialized, starting servo control...
Set servo angle: 135.0 (max: 270.0), PWM: 1500 us
Set servo angle: 90.0 (max: 180.0), PWM: 1500 us
Initial servo positions set. Ready for commands.
Commands: x+10, x-10, y+10, y-10, A, S1, S2
```

### 3. 测试指令
发送以下指令并观察输出：

#### 基本控制测试
```
x+10    // X轴右转10度
```
**预期输出**：
```
Received 4 bytes: x+10
Processing command: x+10
X_Move_Angle = 10.000000
Set servo angle: 145.0 (max: 270.0), PWM: 1574 us
X current_servo_angle = 145.000000
```

#### 重置测试
```
S2      // 重置到中心位置
```
**预期输出**：
```
Received 2 bytes: S2
Processing command: S2
will reset
Set servo angle: 90.0 (max: 270.0), PWM: 1167 us
Set servo angle: 90.0 (max: 180.0), PWM: 1500 us
```

## 硬件连接检查

### 1. 串口连接
- **TX** → PB11 (UART3_RX)
- **RX** → PB10 (UART3_TX)
- **波特率**：115200

### 2. 舵机连接
- **X轴舵机**：连接到PA0 (TIM2_CH1)
- **Y轴舵机**：连接到PA6 (TIM3_CH1)
- **电源**：5V供电
- **地线**：共地连接

### 3. PWM信号检查
使用示波器检查PWM信号：
- **频率**：50Hz
- **脉宽**：500-2500us
- **幅值**：3.3V

## 故障排除

### 问题1：没有串口输出
**可能原因**：
1. 串口连接错误
2. 波特率设置错误
3. 串口软件配置错误

**解决方法**：
1. 检查TX/RX连接（TX接RX，RX接TX）
2. 确认波特率115200
3. 检查串口软件设置

### 问题2：有串口输出但舵机不转
**可能原因**：
1. PWM信号问题
2. 舵机电源问题
3. 舵机连接错误

**解决方法**：
1. 使用示波器检查PWM信号
2. 检查舵机电源（5V，足够电流）
3. 检查信号线连接

### 问题3：舵机转动但角度不对
**可能原因**：
1. 角度计算错误
2. PWM参数错误
3. 舵机零位偏移

**解决方法**：
1. 检查s20_set_angle函数
2. 调整PWM脉宽范围
3. 校准舵机零位

### 问题4：指令响应延迟
**可能原因**：
1. 任务调度频率低
2. 串口接收问题

**解决方法**：
1. 检查scheduler.c中的任务周期（当前20ms）
2. 检查串口接收配置

## 调试技巧

### 1. 观察串口输出
- 确认是否收到数据
- 检查数据解析是否正确
- 验证PWM参数计算

### 2. 使用示波器
- 检查PWM信号频率（50Hz）
- 检查PWM脉宽范围（500-2500us）
- 确认信号质量

### 3. 逐步测试
1. 先测试串口通信
2. 再测试PWM输出
3. 最后测试舵机控制

## 常见指令示例

```
// 基本控制
x+10    // X轴右转10度
x-10    // X轴左转10度
y+10    // Y轴上转10度
y-10    // Y轴下转10度

// 任务控制
A       // 记录当前点
S1      // 执行任务1
S2      // 重置到中心

// 大角度测试
x+50    // X轴右转50度
y+30    // Y轴上转30度
```

如果按照以上步骤测试后仍有问题，请提供具体的串口输出信息，以便进一步诊断。 