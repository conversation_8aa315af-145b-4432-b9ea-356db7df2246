#include "bsp_system.h"
#include "data_precess.h"

// ========== 全局变量 ==========
extern Point_t path_points[PATH_POINTS_NUM];
extern Point_t laser_point;
extern uint8_t path_points_received;

// ========== PID实现 ==========
// PID实现
void PID_Init(PID_Controller_t *pid, float kp, float ki, float kd, float min_out, float max_out) {
    pid->kp = kp; pid->ki = ki; pid->kd = kd;
    pid->integral = 0; pid->last_error = 0; pid->output = 0;
    pid->min_output = min_out; pid->max_output = max_out;
}
float PID_Calculate(float target, float feedback, PID_Controller_t *pid) {
    float error = target - feedback;
    pid->integral += error;
    float derivative = error - pid->last_error;
    pid->output = pid->kp * error + pid->ki * pid->integral + pid->kd * derivative;
    if (pid->output > pid->max_output) pid->output = pid->max_output;
    if (pid->output < pid->min_output) pid->output = pid->min_output;
    pid->last_error = error;
    return pid->output;
}

// 步长限制
float step_limit(float output, float step) {
    if (output > step) return step;
    if (output < -step) return -step;
    return output;
}

// 寻找最近点
int find_nearest_path_point(Point_t laser, Point_t *path, int n) {
    int idx = 0;
    float min_dist = 1e9;
    for (int i = 0; i < n; i++) {
        float dx = path[i].x - laser.x;
        float dy = path[i].y - laser.y;
        float dist = dx*dx + dy*dy;
        if (dist < min_dist) {
            min_dist = dist;
            idx = i;
        }
    }
    return idx;
}

// ========== 路径跟踪 ==========
float map_pid_to_angle(float output, float output_min, float output_max, float angle_min, float angle_max) {
    // 线性映射
    float angle = (output - output_min) * (angle_max - angle_min) / (output_max - output_min) + angle_min;
    // 限幅
    if (angle > angle_max) angle = angle_max;
    if (angle < angle_min) angle = angle_min;
    return angle;
}

void follow_path(void) {
    static PID_Controller_t pid_x, pid_y;
    static uint8_t pid_inited = 0;
    if (!pid_inited) {
        PID_Init(&pid_x, 0.5, 0, 0, -30, 30); // 参数可调
        PID_Init(&pid_y, 0.5, 0, 0, -30, 30);
        pid_inited = 1;
    }
    if (!path_points_received) return;

    int nearest_idx = find_nearest_path_point(laser_point, path_points, PATH_POINTS_NUM);
    float target_x = path_points[nearest_idx].x;
    float target_y = path_points[nearest_idx].y;

    float output_x = PID_Calculate(target_x, laser_point.x, &pid_x);
    float output_y = PID_Calculate(target_y, laser_point.y, &pid_y);

    output_x = step_limit(output_x, 0.5f);
    output_y = step_limit(output_y, 0.5f);

    float angle_x = map_pid_to_angle(output_x, -0.5f, 0.5f, 0.0f, 270.0f);
    float angle_y = map_pid_to_angle(output_y, -0.5f, 0.5f, 0.0f, 180.0f);
    s20_set_angle(angle_x, 270);
    s20_set_angle(angle_y, 180);
}


